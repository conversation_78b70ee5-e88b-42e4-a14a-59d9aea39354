'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { BulkNudgeModal } from '@/components/msme/bulk-nudge-modal';
import { api } from '@/lib/api';
import { MSME, Analytics, TopAction } from '@/types';
import { 
  Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle,
  Search, Filter, BarChart3, MessageSquare, Activity, Target, Zap,
  ArrowUpRight, ArrowDownRight, DollarSign, Shield, Lightbulb, Star
} from 'lucide-react';

export function PortfolioDashboard() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBulkNudgeModal, setShowBulkNudgeModal] = useState(false);
  const [activeTab, setActiveTab] = useState('portfolio');
  const router = useRouter();
  const searchParams = useSearchParams();
  const filterRisk = searchParams?.get('filter');

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const [portfolioData, analyticsData] = await Promise.all([
          api.getPortfolio(),
          api.getAnalytics()
        ]);
        setMsmes(portfolioData);
        setAnalytics(analyticsData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  // Generate top actions based on portfolio analysis
  const generateTopActions = (): TopAction[] => {
    if (!analytics || msmes.length === 0) return [];

    const actions: TopAction[] = [];
    
    // High risk MSMEs requiring immediate attention
    const highRiskCount = analytics.risk_distribution.red;
    if (highRiskCount > 0) {
      actions.push({
        id: 'high_risk_review',
        title: 'Review High-Risk MSMEs',
        description: `${highRiskCount} MSMEs require immediate risk assessment and intervention`,
        impact: 'high',
        type: 'risk_reduction',
        msme_count: highRiskCount,
        potential_value: '₹2.5L+ risk mitigation',
        action_items: [
          'Schedule risk assessment calls',
          'Review recent financial data',
          'Implement enhanced monitoring'
        ],
        priority_score: 95,
        estimated_completion_days: 7
      });
    }

    // Medium risk MSMEs with growth potential
    const mediumRiskCount = analytics.risk_distribution.yellow;
    if (mediumRiskCount > 0) {
      actions.push({
        id: 'growth_opportunities',
        title: 'Unlock Growth Opportunities',
        description: `${mediumRiskCount} medium-risk MSMEs show potential for credit line increases`,
        impact: 'medium',
        type: 'ltv_optimization',
        msme_count: mediumRiskCount,
        potential_value: '₹8.2L+ revenue potential',
        action_items: [
          'Analyze cash flow trends',
          'Evaluate credit utilization',
          'Propose limit increases'
        ],
        priority_score: 75,
        estimated_completion_days: 14
      });
    }

    // Portfolio diversification opportunity
    const dominantType = Object.entries(analytics.business_type_distribution)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (dominantType && dominantType[1] / analytics.total_msmes > 0.6) {
      actions.push({
        id: 'diversification',
        title: 'Portfolio Diversification',
        description: `${Math.round((dominantType[1] / analytics.total_msmes) * 100)}% concentration in ${dominantType[0]} sector`,
        impact: 'medium',
        type: 'portfolio_growth',
        msme_count: Math.floor(analytics.total_msmes * 0.2),
        potential_value: '₹5.8L+ risk reduction',
        action_items: [
          'Target underrepresented sectors',
          'Develop sector-specific products',
          'Partner with industry associations'
        ],
        priority_score: 65,
        estimated_completion_days: 30
      });
    }

    // Automation opportunity
    const lowSignalMsmes = msmes.filter(m => m.signals_count < analytics.average_signals_per_msme * 0.7).length;
    if (lowSignalMsmes > 0) {
      actions.push({
        id: 'data_enhancement',
        title: 'Enhance Data Collection',
        description: `${lowSignalMsmes} MSMEs have insufficient data signals for accurate scoring`,
        impact: 'high',
        type: 'efficiency',
        msme_count: lowSignalMsmes,
        potential_value: '₹3.1L+ efficiency gains',
        action_items: [
          'Implement automated data collection',
          'Integrate additional data sources',
          'Improve signal quality'
        ],
        priority_score: 85,
        estimated_completion_days: 21
      });
    }

    return actions.slice(0, 4).sort((a, b) => b.priority_score - a.priority_score);
  };

  const topActions = generateTopActions();

  const filteredMsmes = msmes.filter(msme => {
    const matchesSearch = searchTerm === '' || 
      msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.business_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = !filterRisk || msme.risk_band === filterRisk;
    
    return matchesSearch && matchesFilter;
  });

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Minus className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getBusinessTypeIcon = (type: string) => {
    switch (type) {
      case 'retail': return '🏪';
      case 'manufacturing': return '🏭';
      case 'services': return '💼';
      case 'b2b': return '🤝';
      default: return '🏢';
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'risk_reduction': return <Shield className="h-5 w-5" />;
      case 'ltv_optimization': return <DollarSign className="h-5 w-5" />;
      case 'portfolio_growth': return <TrendingUp className="h-5 w-5" />;
      case 'efficiency': return <Zap className="h-5 w-5" />;
      default: return <Lightbulb className="h-5 w-5" />;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'risk_reduction': return 'text-red-600 bg-red-50 border-red-200';
      case 'ltv_optimization': return 'text-green-600 bg-green-50 border-green-200';
      case 'portfolio_growth': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'efficiency': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-24 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Portfolio</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              Portfolio Dashboard
              {filterRisk && (
                <span className="ml-2 text-2xl font-normal text-muted-foreground">
                  {getRiskLabel(filterRisk)} Risk
                </span>
              )}
            </h1>
            <div className="flex items-center gap-4 text-muted-foreground">
              <span className="flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                {filteredMsmes.length} of {msmes.length} MSMEs
              </span>
              {analytics && (
                <span className="flex items-center gap-1">
                  <Activity className="h-4 w-4" />
                  {analytics.total_signals} total signals
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Top Actions Section */}
        {topActions.length > 0 && (
          <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/5 to-primary/10">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Star className="h-5 w-5 text-primary" />
                </div>
                Top Opportunities & Actions
              </CardTitle>
              <CardDescription>
                AI-powered recommendations based on risk-LTV analysis and portfolio insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {topActions.map((action) => (
                  <Card key={action.id} className={`border ${getActionColor(action.type)} hover:shadow-md transition-all duration-200`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getActionIcon(action.type)}
                          <CardTitle className="text-base">{action.title}</CardTitle>
                        </div>
                        <Badge variant={action.impact === 'high' ? 'destructive' : action.impact === 'medium' ? 'secondary' : 'outline'} className="text-xs">
                          {action.impact} impact
                        </Badge>
                      </div>
                      <CardDescription className="text-sm">
                        {action.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Affected MSMEs:</span>
                          <span className="font-medium">{action.msme_count}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Potential Value:</span>
                          <span className="font-medium text-green-600">{action.potential_value}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Est. Timeline:</span>
                          <span className="font-medium">{action.estimated_completion_days} days</span>
                        </div>
                        <div className="space-y-1">
                          <span className="text-xs font-medium text-muted-foreground">Action Items:</span>
                          <ul className="text-xs space-y-1">
                            {action.action_items.slice(0, 2).map((item, index) => (
                              <li key={index} className="flex items-center gap-1">
                                <div className="w-1 h-1 bg-current rounded-full" />
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="portfolio">Portfolio Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics & Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="portfolio" className="space-y-6">
            {/* Search and Filters */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search MSMEs by name, location, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 shadow-sm"
                />
              </div>

              <div className="flex gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={filterRisk === 'red' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => router.push('/?filter=red')}
                      className="shadow-sm"
                    >
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      High Risk ({analytics?.risk_distribution.red || 0})
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show only high-risk MSMEs</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={filterRisk === 'yellow' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => router.push('/?filter=yellow')}
                      className="shadow-sm"
                    >
                      <Filter className="mr-2 h-4 w-4" />
                      Medium Risk ({analytics?.risk_distribution.yellow || 0})
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show only medium-risk MSMEs</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={filterRisk === 'green' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => router.push('/?filter=green')}
                      className="shadow-sm"
                    >
                      <Target className="mr-2 h-4 w-4" />
                      Low Risk ({analytics?.risk_distribution.green || 0})
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show only low-risk MSMEs</p>
                  </TooltipContent>
                </Tooltip>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBulkNudgeModal(true)}
                  className="shadow-sm"
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Bulk Nudge
                </Button>
              </div>
            </div>

            {filterRisk && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  Filtered by {getRiskLabel(filterRisk)} Risk
                </Badge>
                <Button
                  variant="link"
                  className="p-0 h-auto text-primary hover:text-primary/80"
                  onClick={() => router.push('/')}
                >
                  Clear filter
                </Button>
              </div>
            )}

            {/* Portfolio Table */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Building2 className="h-5 w-5 text-primary" />
                  </div>
                  Portfolio Overview
                  <Badge variant="secondary" className="ml-auto">
                    {filteredMsmes.length} MSMEs
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Click on any MSME row to view detailed information and score breakdown
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-lg border overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead className="font-semibold">Business</TableHead>
                        <TableHead className="font-semibold">Type</TableHead>
                        <TableHead className="font-semibold">Location</TableHead>
                        <TableHead className="font-semibold text-center">Score</TableHead>
                        <TableHead className="font-semibold text-center">Risk</TableHead>
                        <TableHead className="font-semibold text-center">Trend</TableHead>
                        <TableHead className="font-semibold text-center">Signals</TableHead>
                        <TableHead className="font-semibold">Last Activity</TableHead>
                        <TableHead className="font-semibold text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMsmes.map((msme) => (
                        <TableRow
                          key={msme.msme_id}
                          className="group hover:bg-muted/30 transition-colors duration-200 border-b cursor-pointer"
                          onClick={() => router.push(`/msme/${msme.msme_id}`)}
                        >
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-3">
                              <div className="flex-shrink-0">
                                <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center text-lg">
                                  {getBusinessTypeIcon(msme.business_type)}
                                </div>
                              </div>
                              <div>
                                <div className="font-medium">{msme.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {msme.tags.slice(0, 2).map(tag => (
                                    <Badge key={tag} variant="outline" className="mr-1 text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {msme.business_type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm">
                              <MapPin className="h-3 w-3 text-muted-foreground" />
                              {msme.location}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="font-bold text-lg">{msme.current_score}</div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge variant={getRiskBadgeVariant(msme.risk_band)}>
                              {getRiskLabel(msme.risk_band)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <Tooltip>
                              <TooltipTrigger>
                                {getTrendIcon(msme.score_trend)}
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Score trend: {msme.score_trend || 'stable'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge variant="secondary" className="font-mono">
                              {msme.signals_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground">
                              {msme.last_signal_date ? (
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(msme.last_signal_date).toLocaleDateString()}
                                </div>
                              ) : (
                                'No recent activity'
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/msme/${msme.msme_id}`);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <BarChart3 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {filteredMsmes.length === 0 && (
                  <div className="text-center py-12">
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                        <Building2 className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold">No MSMEs found</h3>
                        <p className="text-muted-foreground">
                          {searchTerm ? (
                            <>No MSMEs match your search criteria &ldquo;{searchTerm}&rdquo;</>
                          ) : filterRisk ? (
                            <>No MSMEs found with {getRiskLabel(filterRisk).toLowerCase()} risk level</>
                          ) : (
                            <>Your portfolio is empty. Add some MSMEs to get started.</>
                          )}
                        </p>
                      </div>
                      {(searchTerm || filterRisk) && (
                        <Button
                          variant="outline"
                          onClick={() => {
                            setSearchTerm('');
                            router.push('/');
                          }}
                        >
                          Clear filters
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Analytics Cards */}
            {analytics && (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Total MSMEs</CardTitle>
                        <div className="p-2 bg-blue-500 rounded-lg">
                          <Building2 className="h-4 w-4 text-white" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-blue-900 dark:text-blue-100">{analytics.total_msmes}</div>
                        <p className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          Active businesses in portfolio
                        </p>
                      </CardContent>
                      <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10" />
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Total number of MSMEs in your portfolio</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Total Signals</CardTitle>
                        <div className="p-2 bg-emerald-500 rounded-lg">
                          <Activity className="h-4 w-4 text-white" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-emerald-900 dark:text-emerald-100">{analytics.total_signals}</div>
                        <p className="text-sm text-emerald-600 dark:text-emerald-400 flex items-center gap-1">
                          <Target className="h-3 w-3" />
                          Avg {analytics.average_signals_per_msme} per MSME
                        </p>
                      </CardContent>
                      <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-500/10 rounded-full -translate-y-10 translate-x-10" />
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Total data signals collected across all MSMEs</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Risk Distribution</CardTitle>
                        <div className="p-2 bg-purple-500 rounded-lg">
                          <BarChart3 className="h-4 w-4 text-white" />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-green-600">Low Risk</span>
                            <span className="font-medium">{analytics.risk_distribution.green}</span>
                          </div>
                          <Progress value={(analytics.risk_distribution.green / analytics.total_msmes) * 100} className="h-2" />
                        </div>
                        <div className="flex gap-2 flex-wrap">
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                            Low: {analytics.risk_distribution.green}
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                            Med: {analytics.risk_distribution.yellow}
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                            High: {analytics.risk_distribution.red}
                          </Badge>
                        </div>
                      </CardContent>
                      <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10" />
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Risk level distribution across your portfolio</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-red-700 dark:text-red-300">High Risk Alert</CardTitle>
                        <div className="p-2 bg-red-500 rounded-lg animate-pulse">
                          <AlertTriangle className="h-4 w-4 text-white" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-red-900 dark:text-red-100">
                          {analytics.risk_distribution.red}
                        </div>
                        <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                          <Zap className="h-3 w-3" />
                          MSMEs need immediate attention
                        </p>
                      </CardContent>
                      <div className="absolute top-0 right-0 w-20 h-20 bg-red-500/10 rounded-full -translate-y-10 translate-x-10" />
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>MSMEs requiring immediate risk assessment</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}

            {/* Business Type Distribution */}
            {analytics && (
              <div className="grid gap-6 lg:grid-cols-2">
                <Card className="border-0 shadow-lg">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Building2 className="h-5 w-5 text-primary" />
                      </div>
                      Business Type Distribution
                    </CardTitle>
                    <CardDescription>
                      Portfolio composition by business category
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(analytics.business_type_distribution).map(([type, count], index) => {
                      const percentage = (count / analytics.total_msmes) * 100;
                      const colors = ['bg-blue-500', 'bg-emerald-500', 'bg-purple-500', 'bg-orange-500'];
                      return (
                        <div key={type} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`} />
                              <span className="capitalize font-medium">{type}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</span>
                              <Badge variant="secondary" className="font-mono">{count}</Badge>
                            </div>
                          </div>
                          <Progress value={percentage} className="h-2" />
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-emerald-500/10 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-emerald-500" />
                      </div>
                      Portfolio Health
                    </CardTitle>
                    <CardDescription>
                      Key performance indicators
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid gap-4">
                      <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-green-500 rounded-lg">
                            <ArrowUpRight className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-green-900 dark:text-green-100">Healthy MSMEs</p>
                            <p className="text-sm text-green-600 dark:text-green-400">Low risk businesses</p>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {analytics.risk_distribution.green}
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-yellow-500 rounded-lg">
                            <Minus className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-yellow-900 dark:text-yellow-100">Watch List</p>
                            <p className="text-sm text-yellow-600 dark:text-yellow-400">Medium risk businesses</p>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                          {analytics.risk_distribution.yellow}
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-red-500 rounded-lg">
                            <ArrowDownRight className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-red-900 dark:text-red-100">Critical</p>
                            <p className="text-sm text-red-600 dark:text-red-400">High risk businesses</p>
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                          {analytics.risk_distribution.red}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Footer */}
            {analytics && (
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
                <Activity className="h-4 w-4" />
                <span>Last updated: {new Date(analytics.last_updated).toLocaleString()}</span>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Bulk Nudge Modal */}
        {showBulkNudgeModal && (
          <BulkNudgeModal
            msmes={filteredMsmes}
            onClose={() => setShowBulkNudgeModal(false)}
          />
        )}
      </div>
    </TooltipProvider>
  );
}
